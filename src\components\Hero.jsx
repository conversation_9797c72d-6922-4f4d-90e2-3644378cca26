import { Link } from 'react-router-dom';
import { ArrowRight, Award, Users, BookOpen } from 'lucide-react';

const Hero = () => {
  return (
    <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Apprenez gratuitement avec des
            <span className="text-yellow-300"> certificats reconnus</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Découvrez des milliers de cours gratuits de qualité avec certificats 
            des meilleures universités et entreprises du monde.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link 
              to="/courses"
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors flex items-center justify-center"
            >
              Explorer les cours
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link 
              to="/categories"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors"
            >
              Parcourir par catégorie
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <BookOpen className="h-8 w-8 text-yellow-300" />
              </div>
              <div className="text-3xl font-bold">1000+</div>
              <div className="text-blue-200">Cours disponibles</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Award className="h-8 w-8 text-yellow-300" />
              </div>
              <div className="text-3xl font-bold">100%</div>
              <div className="text-blue-200">Certificats gratuits</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Users className="h-8 w-8 text-yellow-300" />
              </div>
              <div className="text-3xl font-bold">500K+</div>
              <div className="text-blue-200">Étudiants actifs</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
