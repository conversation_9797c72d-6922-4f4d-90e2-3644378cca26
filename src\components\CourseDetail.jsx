import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { courses } from '../data/courses';
import { 
  Clock, Users, Star, Award, ExternalLink, ArrowLeft, 
  BookOpen, Globe, User, Building 
} from 'lucide-react';

const CourseDetail = () => {
  const { id } = useParams();
  const course = courses.find(c => c.id === parseInt(id));

  if (!course) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Cours non trouvé</h1>
          <Link to="/courses" className="text-blue-600 hover:text-blue-800">
            Retour aux cours
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back button */}
      <Link 
        to="/courses"
        className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Retour aux cours
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main content */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <img 
              src={course.image} 
              alt={course.title}
              className="w-full h-64 object-cover"
            />
            
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  {course.platform}
                </span>
                <div className="flex items-center">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-lg font-semibold ml-1">{course.rating}</span>
                  <span className="text-gray-600 ml-1">({course.students.toLocaleString()} étudiants)</span>
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
              
              <p className="text-gray-700 text-lg mb-6">{course.description}</p>

              {/* Course details */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">Durée</div>
                  <div className="font-semibold">{course.duration}</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <BookOpen className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">Niveau</div>
                  <div className="font-semibold">{course.level}</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <Globe className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">Langue</div>
                  <div className="font-semibold">{course.language}</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <Award className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <div className="text-sm text-gray-600">Certificat</div>
                  <div className="font-semibold text-green-600">Gratuit</div>
                </div>
              </div>

              {/* Skills */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Compétences acquises</h3>
                <div className="flex flex-wrap gap-2">
                  {course.skills.map((skill, index) => (
                    <span 
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-24">
            {/* Instructor */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Instructeur</h3>
              <div className="flex items-center space-x-3">
                <User className="h-10 w-10 text-gray-400 bg-gray-100 rounded-full p-2" />
                <div>
                  <div className="font-medium text-gray-900">{course.instructor}</div>
                  <div className="text-sm text-gray-600 flex items-center">
                    <Building className="h-4 w-4 mr-1" />
                    {course.university}
                  </div>
                </div>
              </div>
            </div>

            {/* Enrollment */}
            <div className="space-y-4">
              <a 
                href={course.enrollmentLink}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
              >
                S'inscrire gratuitement
                <ExternalLink className="h-4 w-4 ml-2" />
              </a>
              
              <div className="text-center text-sm text-gray-600">
                Accès immédiat • Certificat gratuit
              </div>
            </div>

            {/* Course stats */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Étudiants inscrits</span>
                  <span className="font-semibold">{course.students.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Note moyenne</span>
                  <span className="font-semibold">{course.rating}/5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Certificat</span>
                  <span className="font-semibold text-green-600">Inclus</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
