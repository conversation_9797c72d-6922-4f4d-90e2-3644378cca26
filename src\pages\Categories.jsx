import { Link } from 'react-router-dom';
import { categories } from '../data/categories';
import { courses } from '../data/courses';

const Categories = () => {
  // Count courses per category
  const getCourseCount = (categoryId) => {
    if (categoryId === 'all') return courses.length;
    return courses.filter(course => course.category === categoryId).length;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Explorez nos catégories
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Découvrez des cours dans tous les domaines qui vous passionnent
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.filter(cat => cat.id !== 'all').map((category) => {
          const courseCount = getCourseCount(category.id);
          
          return (
            <Link
              key={category.id}
              to={`/courses?category=${category.id}`}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group"
            >
              <div className="text-center">
                <div className="text-6xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-2">
                  {category.name}
                </h3>
                <p className="text-gray-600">
                  {courseCount} cours disponible{courseCount > 1 ? 's' : ''}
                </p>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Popular categories section */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          Catégories populaires
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {['programming', 'data-science', 'design', 'business'].map((categoryId) => {
            const category = categories.find(cat => cat.id === categoryId);
            const courseCount = getCourseCount(categoryId);
            
            return (
              <Link
                key={categoryId}
                to={`/courses?category=${categoryId}`}
                className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4 text-center hover:from-blue-100 hover:to-indigo-200 transition-colors"
              >
                <div className="text-3xl mb-2">{category.icon}</div>
                <div className="font-medium text-gray-900">{category.name}</div>
                <div className="text-sm text-gray-600">{courseCount} cours</div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Categories;
